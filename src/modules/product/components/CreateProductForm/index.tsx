import { Link } from "@tanstack/react-router";
import { ProductTabType } from "../../store/createProductTab";
import ProductInfo from "./ProductInfo";
import ProductionInfo from "./ProductionInfo";
import useCreateProductForm from "./use-create-product-form";

export default function CreateProductForm() {
	const { form, isPending, selectedTab } = useCreateProductForm();

	return (
		<div className="card bg-base-300 shadow-sm">
			<div className="card-body">
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						{selectedTab === ProductTabType.PRODUCT_INFO && (
							<ProductInfo form={form} />
						)}

						{selectedTab === ProductTabType.PRODUCTION_INFO && (
							<ProductionInfo form={form} />
						)}

						<div className="flex justify-end gap-4">
							<Link to="/admin/products/products" className="btn btn-ghost">
								Cancelar
							</Link>
							<form.SubscribeButton
								label="Crear Producto"
								className="btn btn-primary"
								isDisabled={isPending}
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
