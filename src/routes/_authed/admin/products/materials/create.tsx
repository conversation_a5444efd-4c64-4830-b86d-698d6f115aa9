import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import CreateMaterialForm from "~/materials/components/CreateMaterialForm";

export const Route = createFileRoute(
	"/_authed/admin/products/materials/create",
)({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Crear Materiales",
			},
		],
	}),
});

function RouteComponent() {
	return (
		<div className="container mx-auto max-w-4xl">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link to="/admin/products/materials" className="btn btn-ghost btn-sm">
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">Crear Material</h1>
				</div>
			</div>

			<div className="space-y-6">
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">Información del Material</h2>
						<CreateMaterialForm />
					</div>
				</div>
			</div>
		</div>
	);
}
