import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import CreateProductionDeviceForm from "~/production-devices/components/CreateProductionDeviceForm";

export const Route = createFileRoute(
	"/_authed/admin/products/production-devices/create",
)({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Crear Equipos de Producción",
			},
		],
	}),
});

function RouteComponent() {
	return (
		<div className="container mx-auto max-w-4xl">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link
						to="/admin/products/production-devices"
						className="btn btn-ghost btn-sm"
					>
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">
						Crear Dispositivo de Producción
					</h1>
				</div>
			</div>

			<div className="space-y-6">
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">
							Información del Dispositivo de Producción
						</h2>
						<CreateProductionDeviceForm />
					</div>
				</div>
			</div>
		</div>
	);
}
