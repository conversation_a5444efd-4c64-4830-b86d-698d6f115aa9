import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import EditRecipeForm from "src/modules/recipe/components/EditRecipeForm";

export const Route = createFileRoute(
	"/_authed/admin/manufacture/recipes/edit/$id",
)({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Editar Receta",
			},
		],
	}),
});

function RouteComponent() {
	const { id } = Route.useParams();

	return (
		<div className="container mx-auto max-w-4xl">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link
						to="/admin/manufacture/recipes"
						className="btn btn-ghost btn-sm"
					>
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">Editar Receta</h1>
				</div>
			</div>

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<EditRecipeForm id={id} />
				</div>
			</div>
		</div>
	);
}
