import { useQuery } from "@tanstack/react-query";
import { Link, createFileRoute } from "@tanstack/react-router";
import { ArrowLeft } from "lucide-react";
import { useEffect, useState } from "react";
import { useService } from "src/config/context/serviceProvider";
import { getErrorResult } from "src/core/utils/effectErrors";
import EditProductionFlowForm from "src/modules/production-flow/components/EditProductionFlowPage";
import type { ActivitySchema } from "src/modules/production-flow/components/schemas";
import { productionFlowWithActivitiesOptionsById } from "src/modules/production-flow/hooks/production-flow-options";

export const Route = createFileRoute(
	"/_authed/admin/manufacture/production-flow/edit/$id",
)({
	component: RouteComponent,
	head: () => ({
		meta: [
			{
				title: "Editar Flujo de Producción",
			},
		],
	}),
});

function RouteComponent() {
	const { id } = Route.useParams();
	const service = useService();
	const [activities, setActivities] = useState<ActivitySchema[]>([]);

	// Fetch production flow with activities
	const { data, isLoading, error } = useQuery(
		productionFlowWithActivitiesOptionsById(service, id),
	);

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isLoading) {
		return (
			<div className="container mx-auto max-w-4xl">
				<div className="flex h-64 items-center justify-center">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto max-w-4xl">
				<div className="alert alert-error">
					<span>Error: {getErrorResult(error).error.message}</span>
				</div>
			</div>
		);
	}

	if (!data) {
		return (
			<div className="container mx-auto max-w-4xl">
				<div className="flex h-64 items-center justify-center">
					<span className="loading loading-spinner loading-lg" />
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto max-w-4xl">
			<div className="mb-6">
				<div className="mb-4 flex items-center gap-4">
					<Link
						to="/admin/manufacture/production-flow"
						className="btn btn-ghost btn-sm"
					>
						<ArrowLeft size={16} />
						Volver
					</Link>
					<h1 className="font-bold text-2xl">Editar Flujo de Producción</h1>
				</div>
			</div>

			<EditProductionFlowForm
				id={id}
				data={data}
				activities={activities}
				setActivities={setActivities}
			/>
		</div>
	);
}
